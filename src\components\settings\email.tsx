"use client";

import { Button } from "../ui/button";
import * as D from "@/components/ui/dialog";
import { updateEmail } from "@/server/user-crud";
import React from "react";
import { toast } from "../ui/toast";
import { User } from "@/db/infer";
import { Input } from "../ui/input";
import { authClient } from "@/lib/auth-client";

export default ({ user }: { user: User }) => {
  const [open, setOpen] = React.useState<boolean>(false);
  const [email, setEmail] = React.useState<string>("");
  const [loading, setLoading] = React.useState<boolean>(false);

  const reset = () => {
    setEmail("");
    setOpen(false);
  };

  return (
    <D.Dialog open={open} onOpenChange={setOpen}>
      <D.DialogTrigger asChild>
        <Button className="w-full">Update Email</Button>
      </D.DialogTrigger>
      <D.DialogContent>
        <D.DialogHeader>
          <D.DialogTitle>Update Email</D.DialogTitle>
          <D.DialogDescription>Enter your new email address below.</D.DialogDescription>
        </D.DialogHeader>
        <Input type="email" placeholder="New Email" value={email} onChange={(e) => setEmail(e.target.value)} />
        <D.DialogFooter>
          <Button
            disabled={loading || !email}
            onClick={async () => {
              setLoading(true);
              try {
                if (!email.match(/^\S+@\S+\.\S+$/)) throw new Error("Please enter a valid email address");
                if (email.length > 256) throw new Error("Email cannot be longer than 256 characters");
                if (email === user.email) throw new Error("New email cannot be the same as the current email");
                toast.success("Check your inbox for a confirmation email");
                await authClient.changeEmail({
                  newEmail: email,
                  callbackURL: "/dashboard?toast=email-updated",
                });
                reset();
              } catch (e: any) {
                toast.error(e.message);
              }
              setLoading(false);
            }}>
            Update Email
          </Button>
        </D.DialogFooter>
      </D.DialogContent>
    </D.Dialog>
  );
};
