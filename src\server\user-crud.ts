"use server";

import { eq, isNotNull, and } from "drizzle-orm";
import { db } from "@/db";
import { account, user } from "@/db/schema";
import { auth } from "@/lib/auth";

export const getBio = async (userId: string): Promise<string> => {
  const result = await db.select({ bio: user.bio }).from(user).where(eq(user.id, userId));
  return result[0]?.bio || "";
};

export const updateBio = async (userId: string, newBio: string): Promise<void> => {
  if (newBio.length > 512) throw new Error("Bio cannot be longer than 512 characters");
  await db.update(user).set({ bio: newBio }).where(eq(user.id, userId));
};

export const getName = async (userId: string): Promise<string> => {
  const result = await db.select({ name: user.name }).from(user).where(eq(user.id, userId));
  return result[0]?.name || "";
};

export const updateName = async (userId: string, newName: string): Promise<void> => {
  if (newName.length > 64) throw new Error("Name cannot be longer than 64 characters");
  if (newName.trim().length < 6) throw new Error("Name cannot be less than 6 characters");
  await db.update(user).set({ name: newName }).where(eq(user.id, userId));
};

export const hasPassword = async (userId: string): Promise<boolean> => {
  const result = await db
    .select({ id: account.userId })
    .from(account)
    .where(and(eq(account.userId, userId), isNotNull(account.password)))
    .limit(1);

  return result.length > 0;
};

export const setNewPassword = async (newPassword: string, headers: Headers): Promise<void> => {
  if (newPassword.length < 8) throw new Error("Password must be at least 8 characters long");
  if (newPassword.length > 128) throw new Error("Password cannot be longer than 128 characters");
  await auth.api.setPassword({
    body: { newPassword },
    headers,
  });
};

export const updatePassword = async (
  currentPassword: string,
  newPassword: string,
  headers: Headers,
  revoke: boolean
): Promise<{
  token: string | null;
  user: {
    id: string;
    email: string;
    name: string;
    image: string | null | undefined;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
}> => {
  if (newPassword.length < 8) throw new Error("Password must be at least 8 characters long");
  if (newPassword.length > 128) throw new Error("Password cannot be longer than 128 characters");
  const data = await auth.api.changePassword({
    body: {
      newPassword: newPassword,
      currentPassword: currentPassword,
      revokeOtherSessions: revoke,
    },
    headers: headers,
  });
  return data;
};

export const updateEmail = async (userId: string, newEmail: string): Promise<void> => {
  if (newEmail.length > 256) throw new Error("Email cannot be longer than 256 characters");
  if (!newEmail.match(/^\S+@\S+\.\S+$/)) throw new Error("Please enter a valid email address");
  await db.update(user).set({ email: newEmail }).where(eq(user.id, userId));
};
