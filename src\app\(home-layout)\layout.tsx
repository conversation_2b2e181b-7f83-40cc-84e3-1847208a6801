import Sidebar from "@/components/layout/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AvatarProvider } from "@/components/layout/avatar-provider";
import { User } from "@/db/infer";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import AutoToast from "@/components/layout/auto-toast";

export default async ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  return (
    <AvatarProvider initialImage={session?.user.image as string}>
      <SidebarProvider>
        <Sidebar user={session?.user as User} />
        <main className="p-4 md:ml-4 md:pr-8 w-dvw h-dvh overflow-x-hidden relative">
          {children}
          <AutoToast />
        </main>
      </SidebarProvider>
    </AvatarProvider>
  );
};
