import PpUploader from "@/components/settings/pp-uploader";
import { User } from "@/db/infer";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import Bio from "@/components/settings/bio";
import Name from "@/components/settings/name";
import Password from "@/components/settings/password";
import Email from "@/components/settings/email";
import Header from "@/components/layout/header";

export default async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  return (
    <>
      <Header title={"Settings"} description={"Manage your account settings and preferences."} />
      <div className="size-full grid grid-cols-1 lg:grid-cols-[18rem_1fr] gap-4 w-full select-none">
        <div className="flex flex-col gap-2 w-full items-center justify-start">
          <span className="font-semibold text-lg text-left w-full">Profile picture</span>
          <PpUploader user={session?.user as User} />

          <span className="font-semibold text-lg text-left w-full -mb-2">Password</span>
          <Password user={session?.user as User} headers={await headers()} />

          <span className="font-semibold text-lg text-left w-full -mb-2">Email</span>
          <Email user={session?.user as User} />
        </div>
        <div className="flex flex-col gap-4 w-full">
          <Name user={session?.user as User} />
          <Bio user={session?.user as User} />
        </div>
      </div>
    </>
  );
};
