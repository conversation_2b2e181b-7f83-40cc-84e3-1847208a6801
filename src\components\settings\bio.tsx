"use client";

import React from "react";
import { Textarea } from "../ui/textarea";
import { getBio, updateBio } from "@/server/user-crud";
import { toast } from "../ui/toast";
import { User } from "@/db/infer";

const MAX_LENGTH = 512;
const DEBOUNCE_TIME = 3000;

export default ({ user, className }: { user: User; className?: string }) => {
  const [bio, setBio] = React.useState("");
  const [originalBio, setOriginalBio] = React.useState("");
  const [loading, setLoading] = React.useState(true);
  const [saving, setSaving] = React.useState(false);
  const [secondsLeft, setSecondsLeft] = React.useState<number | null>(null);

  const saveTimeout = React.useRef<NodeJS.Timeout | null>(null);
  const intervalRef = React.useRef<NodeJS.Timeout | null>(null);

  const cleanup = () => {
    if (saveTimeout.current) clearTimeout(saveTimeout.current);
    if (intervalRef.current) clearInterval(intervalRef.current);
  };

  const update = async () => {
    cleanup();
    if (saving || bio === originalBio) return;
    if (bio.length > MAX_LENGTH) {
      toast.error(`Bio cannot be longer than ${MAX_LENGTH} characters`);
      return;
    }

    setSaving(true);
    try {
      await updateBio(user.id, bio);
      setOriginalBio(bio);
      toast.success("Bio was saved!");
    } catch {
      toast.error("Failed to save bio");
    } finally {
      setSaving(false);
      setSecondsLeft(null);
    }
  };

  React.useEffect(() => {
    (async () => {
      const initialBio = await getBio(user.id);
      setBio(initialBio);
      setOriginalBio(initialBio);
      setLoading(false);
    })();
  }, [user.id]);

  React.useEffect(() => {
    cleanup();

    if (loading || saving || bio === originalBio || bio.length > MAX_LENGTH) {
      setSecondsLeft(null);
      return;
    }

    setSecondsLeft(DEBOUNCE_TIME / 1000);

    intervalRef.current = setInterval(() => {
      setSecondsLeft((prev) => (prev && prev > 1 ? prev - 1 : null));
    }, 1000);

    saveTimeout.current = setTimeout(update, DEBOUNCE_TIME);

    return cleanup;
  }, [bio, originalBio, loading, saving]);

  return (
    <div className={className}>
      <span className="flex flex-row justify-between items-center text-sm">
        <span className="font-semibold text-lg">
          Bio{" "}
          <span className={`font-light ${bio.length > MAX_LENGTH ? "text-destructive" : "text-muted-foreground"}`}>
            ({bio.length}/{MAX_LENGTH})
          </span>
        </span>
        <span
          className={`${
            bio.length > MAX_LENGTH ? "text-destructive" : "text-muted-foreground"
          } ${secondsLeft || bio.length > MAX_LENGTH ? "opacity-100" : "opacity-10"} 
          transition-all duration-150 text-sm`}>
          {bio.length > MAX_LENGTH ? "Too long" : secondsLeft ? `Saving in ${secondsLeft}s...` : "No changes"}
        </span>
      </span>
      <Textarea
        placeholder="Bio"
        className="min-h-25 max-h-64 resize-y"
        value={bio}
        disabled={loading || saving}
        onChange={(e) => setBio(e.target.value)}
        onKeyDown={(e) => {
          if (e.ctrlKey && e.key === "Enter") {
            e.preventDefault();
            update();
          } else if (e.ctrlKey && e.key.toLowerCase() === "z") {
            e.preventDefault();
            setBio(originalBio);
          }
        }}
      />
    </div>
  );
};
